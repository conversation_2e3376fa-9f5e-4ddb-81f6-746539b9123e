/* 响应式文字排版系统 */

/* 全局文字基础设置 */
* {
  box-sizing: border-box;
}

html {
  /* 基础字体大小使用vw单位，确保响应式 */
  font-size: 0.625vw; /* 16px at 2560px width */
  line-height: var(--line-height-normal);
}

body {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-size: var(--font-md);
  line-height: var(--line-height-normal);
  color: rgba(255, 255, 255, 0.87);
  margin: 0;
  padding: 0;
}

/* 标题层级 */
h1 {
  font-size: var(--font-4xl);
  line-height: var(--line-height-tight);
  margin: 0 0 var(--spacing-v-md) 0;
  font-weight: 700;
}

h2 {
  font-size: var(--font-3xl);
  line-height: var(--line-height-tight);
  margin: 0 0 var(--spacing-v-md) 0;
  font-weight: 600;
}

h3 {
  font-size: var(--font-2xl);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--spacing-v-sm) 0;
  font-weight: 600;
}

h4 {
  font-size: var(--font-xl);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--spacing-v-sm) 0;
  font-weight: 500;
}

h5 {
  font-size: var(--font-lg);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--spacing-v-xs) 0;
  font-weight: 500;
}

h6 {
  font-size: var(--font-md);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--spacing-v-xs) 0;
  font-weight: 500;
}

/* 段落文字 */
p {
  font-size: var(--font-md);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-v-md) 0;
}

/* 小字体文本 */
small {
  font-size: var(--font-sm);
  line-height: var(--line-height-normal);
}

/* 强调文本 */
strong, b {
  font-weight: 700;
}

em, i {
  font-style: italic;
}

/* 链接 */
a {
  color: #00e4ff;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #6ae6ff;
  text-decoration: underline;
}

/* 列表 */
ul, ol {
  margin: 0 0 var(--spacing-v-md) 0;
  padding-left: var(--spacing-lg);
}

li {
  font-size: var(--font-md);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-v-xs);
}

/* 代码文本 */
code {
  font-family: 'Courier New', monospace;
  font-size: var(--font-sm);
  background: rgba(0, 228, 255, 0.1);
  padding: 0.2em 0.4em;
  border-radius: var(--radius-sm);
  color: #00e4ff;
}

pre {
  font-family: 'Courier New', monospace;
  font-size: var(--font-sm);
  line-height: var(--line-height-relaxed);
  background: rgba(0, 20, 80, 0.3);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin: 0 0 var(--spacing-v-md) 0;
}

/* 表格 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 0 0 var(--spacing-v-md) 0;
}

th, td {
  font-size: var(--font-md);
  line-height: var(--line-height-normal);
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid rgba(0, 228, 255, 0.2);
}

th {
  font-weight: 600;
  color: #00e4ff;
  background: rgba(0, 228, 255, 0.1);
}

/* 响应式文字工具类 */
.text-responsive {
  font-size: clamp(var(--font-sm), var(--font-md), var(--font-lg));
}

.title-responsive {
  font-size: clamp(var(--font-xl), var(--font-2xl), var(--font-3xl));
}

.heading-responsive {
  font-size: clamp(var(--font-lg), var(--font-xl), var(--font-2xl));
}

/* 文字颜色 */
.text-primary { color: #00e4ff; }
.text-secondary { color: #6ae6ff; }
.text-accent { color: #a6f9ff; }
.text-muted { color: rgba(255, 255, 255, 0.6); }
.text-white { color: #ffffff; }

/* 文字对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 文字装饰 */
.text-underline { text-decoration: underline; }
.text-no-underline { text-decoration: none; }
.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }

/* 文字阴影效果 */
.text-glow {
  text-shadow: 0 0 var(--spacing-sm) rgba(0, 228, 255, 0.5);
}

.text-glow-strong {
  text-shadow: 0 0 var(--spacing-md) rgba(0, 228, 255, 0.8);
}

/* 响应式断点调整 */
@media (max-width: 1920px) {
  html {
    font-size: 0.7vw; /* 稍微增大基础字体 */
  }
}

@media (max-width: 1366px) {
  html {
    font-size: 0.8vw; /* 进一步增大基础字体 */
  }
}

@media (max-width: 1024px) {
  html {
    font-size: 1vw; /* 平板设备 */
  }
}

@media (max-width: 768px) {
  html {
    font-size: 16px; /* 移动设备使用固定字体 */
  }
  
  /* 移动设备文字调整 */
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.125rem; }
  h6 { font-size: 1rem; }
  
  p, li { font-size: 1rem; }
  small { font-size: 0.875rem; }
}

/* 确保最小可读性 */
@media (max-height: 600px) {
  html {
    font-size: max(0.8vw, 12px); /* 确保最小字体大小 */
  }
}

/* 大屏优化 */
@media (min-width: 2560px) {
  html {
    font-size: 0.625vw; /* 大屏保持原始比例 */
  }
}

/* 超宽屏优化 */
@media (min-width: 3840px) {
  html {
    font-size: 0.5vw; /* 4K屏幕字体调整 */
  }
}
